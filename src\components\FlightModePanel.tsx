import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Plane, Target, Mountain, Navigation, Gamepad2 } from "lucide-react";
import { FlightData } from "@/services/flightControllerAPI";

interface FlightModePanelProps {
  flightData: FlightData;
  onFlightModeChange: (mode: string) => void;
}

const flightModes = [
  {
    id: "MANUAL",
    name: "Manual",
    description: "Direct RC control, no stabilization",
    icon: Gamepad2,
    color: "text-destructive",
    requirements: ["RC Signal"]
  },
  {
    id: "STABILIZE",
    name: "Stabilize",
    description: "Auto-leveling with manual control",
    icon: Plane,
    color: "text-primary",
    requirements: ["RC Signal", "IMU"]
  },
  {
    id: "ALTITUDE_HOLD",
    name: "Altitude Hold",
    description: "Maintain current altitude automatically",
    icon: Mountain,
    color: "text-accent",
    requirements: ["RC Signal", "IMU", "Barometer"]
  },
  {
    id: "GPS_HOLD",
    name: "GPS Hold",
    description: "Hold position using GPS",
    icon: Target,
    color: "text-warning",
    requirements: ["RC Signal", "IMU", "GPS Lock"]
  },
  {
    id: "ACRO",
    name: "Acro",
    description: "Rate mode for aerobatics",
    icon: Navigation,
    color: "text-primary-glow",
    requirements: ["RC Signal", "IMU"]
  }
];

export function FlightModePanel({ flightData, onFlightModeChange }: FlightModePanelProps) {
  const handleModeChange = (mode: string) => {
    onFlightModeChange(mode);
  };

  const isRequirementMet = (requirement: string) => {
    switch (requirement) {
      case "RC Signal":
        return true; // Always true if we have telemetry
      case "IMU":
        return true; // Always true if we have telemetry
      case "Barometer":
        return true; // Always true if we have telemetry
      case "GPS Lock":
        return flightData.gps.connected && flightData.gps.satellites >= 6;
      default:
        return false;
    }
  };

  const canUseMode = (mode: any) => {
    return mode.requirements.every(isRequirementMet);
  };

  return (
    <div className="space-y-6">
      {/* Current Mode Status */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
            <span>Current Flight Mode</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {flightModes.find(m => m.id === flightData.flightMode) && (
                <>
                  <div className={`p-3 rounded-lg bg-primary/10 ${flightModes.find(m => m.id === flightData.flightMode)?.color}`}>
                    {React.createElement(flightModes.find(m => m.id === flightData.flightMode)?.icon || Plane, { className: "h-6 w-6" })}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{flightModes.find(m => m.id === flightData.flightMode)?.name}</h3>
                    <p className="text-muted-foreground">{flightModes.find(m => m.id === flightData.flightMode)?.description}</p>
                  </div>
                </>
              )}
            </div>
            <Badge variant="outline" className="bg-primary/20 text-primary border-primary/50 font-mono text-lg px-4 py-2">
              {flightData.flightMode}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Flight Mode Selection */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader>
          <CardTitle>Select Flight Mode</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup value={flightData.flightMode} onValueChange={handleModeChange}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {flightModes.map((mode) => (
                <div key={mode.id} className={`relative ${!canUseMode(mode) ? 'opacity-50' : ''}`}>
                  <Label
                    htmlFor={mode.id}
                    className={`flex items-start space-x-3 p-4 rounded-lg border-2 cursor-pointer transition-all hover:border-primary/50 ${
                      flightData.flightMode === mode.id 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:bg-card/50'
                    }`}
                  >
                    <RadioGroupItem 
                      value={mode.id} 
                      id={mode.id} 
                      disabled={!canUseMode(mode)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <mode.icon className={`h-5 w-5 ${mode.color}`} />
                        <span className="font-semibold">{mode.name}</span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {mode.description}
                      </p>
                      <div className="space-y-1">
                        <div className="text-xs font-medium text-muted-foreground">Requirements:</div>
                        <div className="flex flex-wrap gap-1">
                          {mode.requirements.map((req) => (
                            <Badge
                              key={req}
                              variant={isRequirementMet(req) ? "default" : "destructive"}
                              className="text-xs"
                            >
                              {req}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Label>
                  {!canUseMode(mode) && (
                    <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-lg">
                      <Badge variant="destructive">Requirements Not Met</Badge>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Flight Mode Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-gradient-surface border-border/50">
          <CardHeader>
            <CardTitle>Mode Switches</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="auto-arm">Auto-Arm on Mode Switch</Label>
              <Switch id="auto-arm" />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="failsafe">Return to Stabilize on Failsafe</Label>
              <Switch id="failsafe" defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="gps-rescue">GPS Rescue Mode</Label>
              <Switch id="gps-rescue" />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="land-mode">Emergency Land Mode</Label>
              <Switch id="land-mode" defaultChecked />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-surface border-border/50">
          <CardHeader>
            <CardTitle>Safety Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Failsafe Throttle (%)</Label>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">0%</span>
                <div className="flex-1 h-2 bg-muted rounded">
                  <div className="h-full w-1/4 bg-destructive rounded"></div>
                </div>
                <span className="text-sm text-muted-foreground">100%</span>
              </div>
              <div className="text-sm text-center font-mono text-destructive">25%</div>
            </div>

            <div className="space-y-2">
              <Label>Low Battery Action</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input type="radio" name="low-battery" id="warn" defaultChecked />
                  <Label htmlFor="warn">Warning Only</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="radio" name="low-battery" id="land" />
                  <Label htmlFor="land">Auto Land</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="radio" name="low-battery" id="return" />
                  <Label htmlFor="return">Return to Home</Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-gradient-surface border-border/50">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button 
              variant="outline" 
              className="h-20 flex-col space-y-2 border-accent/50 hover:border-accent hover:bg-accent/10"
              onClick={() => handleModeChange("STABILIZE")}
            >
              <Plane className="h-6 w-6" />
              <span>Stabilize</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-20 flex-col space-y-2 border-accent/50 hover:border-accent hover:bg-accent/10"
              onClick={() => handleModeChange("ALTITUDE_HOLD")}
            >
              <Mountain className="h-6 w-6" />
              <span>Alt Hold</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-20 flex-col space-y-2 border-accent/50 hover:border-accent hover:bg-accent/10"
              onClick={() => handleModeChange("GPS_HOLD")}
              disabled={!canUseMode(flightModes.find(m => m.id === "GPS_HOLD")!)}
            >
              <Target className="h-6 w-6" />
              <span>GPS Hold</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-20 flex-col space-y-2 border-destructive/50 hover:border-destructive hover:bg-destructive/10"
              onClick={() => handleModeChange("MANUAL")}
            >
              <Gamepad2 className="h-6 w-6" />
              <span>Manual</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Add React import for createElement
import React from "react";
