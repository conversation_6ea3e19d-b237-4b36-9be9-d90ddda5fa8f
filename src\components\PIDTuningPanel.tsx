import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Settings, RotateCcw, Save, Download, Upload } from "lucide-react";
import { FlightData } from "@/services/flightControllerAPI";

interface PIDTuningPanelProps {
  flightData: FlightData;
  onPIDUpdate: (pidData: any) => void;
}

export function PIDTuningPanel({ flightData, onPIDUpdate }: PIDTuningPanelProps) {
  const [localPID, setLocalPID] = useState(flightData.pid);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const updatePID = (axis: 'roll' | 'pitch' | 'yaw', param: 'p' | 'i' | 'd', value: number) => {
    const newPID = {
      ...localPID,
      [axis]: {
        ...localPID[axis],
        [param]: value
      }
    };
    setLocalPID(newPID);
    setHasUnsavedChanges(true);
  };

  const resetToDefaults = () => {
    const defaultPID = {
      roll: { p: 0.04, i: 0.02, d: 0.01 },
      pitch: { p: 0.04, i: 0.02, d: 0.01 },
      yaw: { p: 0.08, i: 0.02, d: 0.00 }
    };
    setLocalPID(defaultPID);
    setHasUnsavedChanges(true);
  };

  const saveSettings = () => {
    onPIDUpdate(localPID);
    setHasUnsavedChanges(false);
  };

  const PIDAxisControls = ({ axis, values }: { axis: string; values: { p: number; i: number; d: number } }) => (
    <Card className="bg-gradient-surface border-border/50">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg capitalize">{axis} Axis</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">Proportional (P)</label>
            <span className="font-mono text-primary bg-primary/10 px-2 py-1 rounded text-sm">
              {values.p.toFixed(4)}
            </span>
          </div>
          <Slider
            value={[values.p]}
            onValueChange={([value]) => updatePID(axis as any, 'p', value)}
            max={0.2}
            min={0}
            step={0.001}
            className="w-full"
          />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">Integral (I)</label>
            <span className="font-mono text-accent bg-accent/10 px-2 py-1 rounded text-sm">
              {values.i.toFixed(4)}
            </span>
          </div>
          <Slider
            value={[values.i]}
            onValueChange={([value]) => updatePID(axis as any, 'i', value)}
            max={0.1}
            min={0}
            step={0.001}
            className="w-full"
          />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">Derivative (D)</label>
            <span className="font-mono text-warning bg-warning/10 px-2 py-1 rounded text-sm">
              {values.d.toFixed(4)}
            </span>
          </div>
          <Slider
            value={[values.d]}
            onValueChange={([value]) => updatePID(axis as any, 'd', value)}
            max={0.05}
            min={0}
            step={0.001}
            className="w-full"
          />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Control Bar */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-primary" />
                <span className="font-semibold">PID Tuning Controller</span>
              </div>
              {hasUnsavedChanges && (
                <Badge variant="outline" className="bg-warning/20 text-warning border-warning/50 animate-pulse">
                  Unsaved Changes
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={resetToDefaults}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button 
                onClick={saveSettings} 
                disabled={!hasUnsavedChanges}
                className="bg-primary hover:bg-primary/90 shadow-glow"
              >
                <Save className="h-4 w-4 mr-2" />
                Save to FC
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-card/50 backdrop-blur-sm border border-border/50">
          <TabsTrigger value="basic">Basic PID</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="filters">Filters</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <PIDAxisControls axis="roll" values={localPID.roll} />
            <PIDAxisControls axis="pitch" values={localPID.pitch} />
            <PIDAxisControls axis="yaw" values={localPID.yaw} />
          </div>

          {/* PID Response Preview */}
          <Card className="mt-6 bg-gradient-surface border-border/50">
            <CardHeader>
              <CardTitle>PID Response Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-mono text-primary mb-2">
                    {((localPID.roll.p + localPID.roll.i + localPID.roll.d) * 100).toFixed(1)}
                  </div>
                  <div className="text-sm text-muted-foreground">Roll Responsiveness</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-mono text-primary mb-2">
                    {((localPID.pitch.p + localPID.pitch.i + localPID.pitch.d) * 100).toFixed(1)}
                  </div>
                  <div className="text-sm text-muted-foreground">Pitch Responsiveness</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-mono text-primary mb-2">
                    {((localPID.yaw.p + localPID.yaw.i + localPID.yaw.d) * 100).toFixed(1)}
                  </div>
                  <div className="text-sm text-muted-foreground">Yaw Responsiveness</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Rate Control</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Roll Rate (°/s)</label>
                  <Slider defaultValue={[720]} max={1800} min={180} step={10} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Pitch Rate (°/s)</label>
                  <Slider defaultValue={[720]} max={1800} min={180} step={10} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Yaw Rate (°/s)</label>
                  <Slider defaultValue={[400]} max={1000} min={100} step={10} />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Loop Timing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">PID Loop Rate (Hz)</label>
                  <Slider defaultValue={[1000]} max={2000} min={500} step={100} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Gyro Update Rate (Hz)</label>
                  <Slider defaultValue={[1000]} max={2000} min={500} step={100} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Motor Update Rate (Hz)</label>
                  <Slider defaultValue={[480]} max={1000} min={240} step={10} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="filters" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Gyro Filters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Low Pass Filter (Hz)</label>
                  <Slider defaultValue={[100]} max={500} min={50} step={10} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Notch Filter Center (Hz)</label>
                  <Slider defaultValue={[260]} max={500} min={100} step={10} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Notch Filter Width (Hz)</label>
                  <Slider defaultValue={[40]} max={100} min={10} step={5} />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>D-Term Filters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">D-Term Low Pass (Hz)</label>
                  <Slider defaultValue={[150]} max={300} min={50} step={10} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">D-Term Notch Center (Hz)</label>
                  <Slider defaultValue={[260]} max={500} min={100} step={10} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">D-Term Notch Width (Hz)</label>
                  <Slider defaultValue={[40]} max={100} min={10} step={5} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
