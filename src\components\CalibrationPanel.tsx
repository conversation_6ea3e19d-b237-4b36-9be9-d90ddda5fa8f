import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Compass, Target, BarChart3, Gauge, CheckCircle, AlertTriangle } from "lucide-react";

interface CalibrationStep {
  id: string;
  name: string;
  description: string;
  icon: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
}

export function CalibrationPanel() {
  const [calibrationSteps, setCalibrationSteps] = useState<CalibrationStep[]>([
    {
      id: 'gyro',
      name: 'Gyroscope Calibration',
      description: 'Keep the drone perfectly still for 10 seconds',
      icon: Gauge,
      status: 'completed',
      progress: 100
    },
    {
      id: 'accel',
      name: 'Accelerometer Calibration',
      description: 'Position drone in 6 orientations as guided',
      icon: Target,
      status: 'completed',
      progress: 100
    },
    {
      id: 'compass',
      name: 'Compass Calibration',
      description: 'Rotate drone slowly in all orientations',
      icon: Compass,
      status: 'pending',
      progress: 0
    },
    {
      id: 'esc',
      name: 'ESC Calibration',
      description: 'Calibrate Electronic Speed Controllers',
      icon: BarChart3,
      status: 'pending',
      progress: 0
    }
  ]);

  const [isCalibrating, setIsCalibrating] = useState(false);
  const [currentStep, setCurrentStep] = useState<string | null>(null);

  const startCalibration = (stepId: string) => {
    setIsCalibrating(true);
    setCurrentStep(stepId);
    
    setCalibrationSteps(prev => 
      prev.map(step => 
        step.id === stepId 
          ? { ...step, status: 'running', progress: 0 }
          : step
      )
    );

    // Simulate calibration progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setCalibrationSteps(prev => 
        prev.map(step => 
          step.id === stepId 
            ? { ...step, progress }
            : step
        )
      );

      if (progress >= 100) {
        clearInterval(interval);
        setCalibrationSteps(prev => 
          prev.map(step => 
            step.id === stepId 
              ? { ...step, status: 'completed', progress: 100 }
              : step
          )
        );
        setIsCalibrating(false);
        setCurrentStep(null);
      }
    }, 500);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">Completed</Badge>;
      case 'running':
        return <Badge variant="default" className="bg-primary/20 text-primary border-primary/50 animate-pulse">Running</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-accent" />;
      case 'failed':
        return <AlertTriangle className="h-5 w-5 text-destructive" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Calibration Overview */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-primary" />
            <span>Sensor Calibration Center</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-mono text-accent mb-2">
                {calibrationSteps.filter(s => s.status === 'completed').length}/{calibrationSteps.length}
              </div>
              <div className="text-sm text-muted-foreground">Sensors Calibrated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-mono text-primary mb-2">
                {Math.round(calibrationSteps.reduce((acc, step) => acc + step.progress, 0) / calibrationSteps.length)}%
              </div>
              <div className="text-sm text-muted-foreground">Overall Progress</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-mono text-warning mb-2">
                {calibrationSteps.filter(s => s.status === 'pending').length}
              </div>
              <div className="text-sm text-muted-foreground">Remaining</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-mono text-destructive mb-2">
                {calibrationSteps.filter(s => s.status === 'failed').length}
              </div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Calibration Status */}
      {isCalibrating && currentStep && (
        <Alert className="border-primary/50 bg-primary/10">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-semibold">
                Calibrating: {calibrationSteps.find(s => s.id === currentStep)?.name}
              </div>
              <div className="text-sm">
                {calibrationSteps.find(s => s.id === currentStep)?.description}
              </div>
              <Progress 
                value={calibrationSteps.find(s => s.id === currentStep)?.progress || 0} 
                className="w-full"
              />
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Calibration Steps */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {calibrationSteps.map((step) => (
          <Card key={step.id} className="bg-gradient-surface border-border/50">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <step.icon className="h-5 w-5 text-primary" />
                  <span>{step.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(step.status)}
                  {getStatusBadge(step.status)}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground text-sm">{step.description}</p>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span className="font-mono">{step.progress}%</span>
                </div>
                <Progress value={step.progress} className="w-full" />
              </div>

              <Button 
                onClick={() => startCalibration(step.id)}
                disabled={isCalibrating || step.status === 'completed'}
                className="w-full"
                variant={step.status === 'completed' ? 'outline' : 'default'}
              >
                {step.status === 'completed' ? 'Recalibrate' : 
                 step.status === 'running' ? 'Calibrating...' : 
                 'Start Calibration'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Manual Calibration Instructions */}
      <Card className="bg-gradient-surface border-border/50">
        <CardHeader>
          <CardTitle>Manual Calibration Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold flex items-center space-x-2">
                <Gauge className="h-4 w-4 text-primary" />
                <span>Gyroscope Calibration</span>
              </h4>
              <ol className="text-sm space-y-2 text-muted-foreground ml-6">
                <li>1. Place drone on a completely level surface</li>
                <li>2. Ensure no vibrations or movement</li>
                <li>3. Click "Start Calibration" and wait 10 seconds</li>
                <li>4. Do not touch or move the drone during calibration</li>
              </ol>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold flex items-center space-x-2">
                <Target className="h-4 w-4 text-primary" />
                <span>Accelerometer Calibration</span>
              </h4>
              <ol className="text-sm space-y-2 text-muted-foreground ml-6">
                <li>1. Position drone level (default position)</li>
                <li>2. Rotate to nose up (90° forward tilt)</li>
                <li>3. Rotate to nose down (90° backward tilt)</li>
                <li>4. Rotate to left side down (90° left roll)</li>
                <li>5. Rotate to right side down (90° right roll)</li>
                <li>6. Position upside down</li>
              </ol>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold flex items-center space-x-2">
                <Compass className="h-4 w-4 text-primary" />
                <span>Compass Calibration</span>
              </h4>
              <ol className="text-sm space-y-2 text-muted-foreground ml-6">
                <li>1. Move away from metal objects and electronics</li>
                <li>2. Hold drone and rotate slowly around all axes</li>
                <li>3. Continue until calibration completes</li>
                <li>4. Avoid areas with magnetic interference</li>
              </ol>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-primary" />
                <span>ESC Calibration</span>
              </h4>
              <ol className="text-sm space-y-2 text-muted-foreground ml-6">
                <li>1. Remove propellers for safety</li>
                <li>2. Connect battery with throttle at maximum</li>
                <li>3. Wait for ESC beep sequence</li>
                <li>4. Move throttle to minimum position</li>
                <li>5. Wait for confirmation beeps</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Calibration Results */}
      <Card className="bg-gradient-surface border-border/50">
        <CardHeader>
          <CardTitle>Last Calibration Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-lg font-mono text-accent mb-1">±0.02°/s</div>
              <div className="text-sm text-muted-foreground">Gyro Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-mono text-accent mb-1">±0.05m/s²</div>
              <div className="text-sm text-muted-foreground">Accel Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-mono text-accent mb-1">±2.1°</div>
              <div className="text-sm text-muted-foreground">Compass Accuracy</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}