/*
 * QuadFly Flight Controller v2.0
 * ESP32-based Open Source Flight Controller
 * 
 * Hardware Requirements:
 * - ESP32 Dev Module
 * - GY-86 Sensor Module (MPU6050 + HMC5883L + MS5611)
 * - GPS Module (NEO-6M/8M)
 * - 8 Channel RC Receiver
 * - 4 ESCs/Motors
 * - TFT Display (ST7735)
 * 
 * Pin Configuration as per PRD
 */

#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include <Preferences.h>
#include <Wire.h>
#include <SPI.h>
#include <ESP32Servo.h>

// Sensor Libraries
#include <MPU6050_tockn.h>
#include <QMC5883LCompass.h>
#include <MS5611.h>
#include <TinyGPS++.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>

// Pin Definitions
#define I2C_SDA 21
#define I2C_SCL 22

// RC Input Pins
#define RC_THROTTLE_PIN 14
#define RC_ROLL_PIN 13
#define RC_PITCH_PIN 12
#define RC_YAW_PIN 27
#define RC_AUX1_PIN 26
#define RC_AUX2_PIN 25
#define RC_AUX3_PIN 36
#define RC_AUX4_PIN 34

// Motor Output Pins
#define MOTOR_FL_PIN 33  // Front Left
#define MOTOR_FR_PIN 32  // Front Right
#define MOTOR_BL_PIN 15  // Back Left
#define MOTOR_BR_PIN 19  // Back Right

// Display Pins
#define TFT_CS 5
#define TFT_RST 4
#define TFT_DC 2

// Other Pins
#define STATUS_LED_PIN 2
#define BATTERY_PIN 35
#define GPS_RX_PIN 16
#define GPS_TX_PIN 17

// Flight Controller Objects
MPU6050 mpu(Wire);
QMC5883LCompass compass;
MS5611 barometer;
TinyGPSPlus gps;
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS, TFT_DC, TFT_RST);
HardwareSerial gpsSerial(2);

// Servo objects for motors
Servo motorFL, motorFR, motorBL, motorBR;

// Web Server
WebServer server(80);
Preferences preferences;

// Flight Controller State
struct FlightData {
  bool armed = false;
  String flightMode = "STABILIZE";
  
  // Battery
  float batteryVoltage = 12.4;
  int batteryPercentage = 85;
  
  // GPS
  bool gpsConnected = false;
  int gpsSatellites = 0;
  double gpsLatitude = 0.0;
  double gpsLongitude = 0.0;
  double gpsAltitude = 0.0;
  
  // Sensors
  float gyroX = 0, gyroY = 0, gyroZ = 0;
  float accelX = 0, accelY = 0, accelZ = 0;
  float magX = 0, magY = 0, magZ = 0;
  float pressure = 1013.25;
  float altitude = 0;
  float temperature = 25.0;
  
  // Attitude
  float roll = 0, pitch = 0, yaw = 0;
  
  // RC Inputs
  int rcThrottle = 1000, rcRoll = 1500, rcPitch = 1500, rcYaw = 1500;
  int rcAux1 = 1000, rcAux2 = 1000, rcAux3 = 1000, rcAux4 = 1000;
  
  // Motor Outputs
  int motorValues[4] = {1000, 1000, 1000, 1000};
  
  // PID Settings
  struct PIDSettings {
    float p = 0.04, i = 0.02, d = 0.01;
  } pidRoll, pidPitch, pidYaw;
  
  // System Status
  bool sensorsOK = true;
  bool calibrated = true;
  unsigned long lastUpdate = 0;
} flightData;

// WiFi Configuration
const char* ap_ssid = "QuadFly_FC_001";
const char* ap_password = "quadfly123";

// PID Controllers
class PIDController {
public:
  float kp, ki, kd;
  float errorSum = 0;
  float lastError = 0;
  unsigned long lastTime = 0;
  
  PIDController(float p, float i, float d) : kp(p), ki(i), kd(d) {}
  
  float compute(float setpoint, float input) {
    unsigned long now = millis();
    float dt = (now - lastTime) / 1000.0;
    if (dt <= 0) return 0;
    
    float error = setpoint - input;
    errorSum += error * dt;
    float dError = (error - lastError) / dt;
    
    float output = kp * error + ki * errorSum + kd * dError;
    
    lastError = error;
    lastTime = now;
    
    return output;
  }
  
  void reset() {
    errorSum = 0;
    lastError = 0;
    lastTime = millis();
  }
};

PIDController rollPID(0.04, 0.02, 0.01);
PIDController pitchPID(0.04, 0.02, 0.01);
PIDController yawPID(0.08, 0.02, 0.00);

void setup() {
  Serial.begin(115200);
  Serial.println("QuadFly Flight Controller v2.0 Starting...");
  
  // Initialize I2C
  Wire.begin(I2C_SDA, I2C_SCL);
  delay(200); 
  // Initialize sensors
  initializeSensors();
  
  // Initialize motors
  initializeMotors();
  
  // Initialize RC inputs
  initializeRC();
  
  // Initialize display
  initializeDisplay();
  
  // Initialize WiFi and web server
  initializeWiFi();
  initializeWebServer();
  
  // Initialize preferences
  preferences.begin("quadfly", false);
  loadSettings();
  
  // Initialize GPS
  gpsSerial.begin(38400, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);
  
  // Status LED
  pinMode(STATUS_LED_PIN, OUTPUT);
  
  Serial.println("QuadFly Flight Controller Ready!");
  blinkStatusLED(3);
}

void loop() {
  unsigned long currentTime = millis();
  
  // Main flight control loop (1kHz)
  static unsigned long lastFlightLoop = 0;
  if (currentTime - lastFlightLoop >= 1) {
    flightControlLoop();
    lastFlightLoop = currentTime;
  }
  
  // Sensor reading loop (100Hz)
  static unsigned long lastSensorLoop = 0;
  if (currentTime - lastSensorLoop >= 10) {
    readSensors();
    lastSensorLoop = currentTime;
  }
  
  // RC input reading (50Hz)
  static unsigned long lastRCLoop = 0;
  if (currentTime - lastRCLoop >= 20) {
    readRCInputs();
    lastRCLoop = currentTime;
  }
  
  // GPS reading (10Hz)
  static unsigned long lastGPSLoop = 0;
  if (currentTime - lastGPSLoop >= 100) {
    readGPS();
    lastGPSLoop = currentTime;
  }
  
  // Display update (10Hz)
  static unsigned long lastDisplayLoop = 0;
  if (currentTime - lastDisplayLoop >= 100) {
    updateDisplay();
    lastDisplayLoop = currentTime;
  }
  
  // Web server handling
  server.handleClient();
  
  // Battery monitoring
  static unsigned long lastBatteryCheck = 0;
  if (currentTime - lastBatteryCheck >= 1000) {
    checkBattery();
    lastBatteryCheck = currentTime;
  }
  
  // Safety checks
  static unsigned long lastSafetyCheck = 0;
  if (currentTime - lastSafetyCheck >= 100) {
    safetyChecks();
    lastSafetyCheck = currentTime;
  }
  
  flightData.lastUpdate = currentTime;
}

void initializeSensors() {
  Serial.println("Initializing sensors...");
  
  // MPU6050 (Gyro/Accel)
  mpu.begin();
  mpu.calcGyroOffsets(true);
  
  // Compass
  compass.init();
  compass.setCalibration(-2866, 1999, -6354, 1652, -2746, 2094);
  
  // Barometer
  if (barometer.begin()) {
    Serial.println("MS5611 Barometer initialized");
  } else {
    Serial.println("MS5611 Barometer initialization failed");
  }
  
  Serial.println("Sensors initialized");
}

void initializeMotors() {
  Serial.println("Initializing motors...");
  
  motorFL.attach(MOTOR_FL_PIN, 1000, 2000);
  motorFR.attach(MOTOR_FR_PIN, 1000, 2000);
  motorBL.attach(MOTOR_BL_PIN, 1000, 2000);
  motorBR.attach(MOTOR_BR_PIN, 1000, 2000);
  
  // Set all motors to minimum
  setAllMotors(1000);
  
  Serial.println("Motors initialized");
}

void initializeRC() {
  Serial.println("Initializing RC inputs...");
  
  pinMode(RC_THROTTLE_PIN, INPUT);
  pinMode(RC_ROLL_PIN, INPUT);
  pinMode(RC_PITCH_PIN, INPUT);
  pinMode(RC_YAW_PIN, INPUT);
  pinMode(RC_AUX1_PIN, INPUT);
  pinMode(RC_AUX2_PIN, INPUT);
  pinMode(RC_AUX3_PIN, INPUT);
  pinMode(RC_AUX4_PIN, INPUT);
  
  Serial.println("RC inputs initialized");
}

void initializeDisplay() {
  Serial.println("Initializing display...");
  
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(1);
  tft.fillScreen(ST77XX_BLACK);
  tft.setTextColor(ST77XX_CYAN);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("QuadFly FC v2.0");
  tft.println("Initializing...");
  
  Serial.println("Display initialized");
}

void initializeWiFi() {
  Serial.println("Setting up WiFi Access Point...");
  
IPAddress local_IP(192, 168, 0, 100);
IPAddress gateway(192, 168, 0, 1);
IPAddress subnet(255, 255, 255, 0);

WiFi.softAPConfig(local_IP, gateway, subnet);
  WiFi.mode(WIFI_AP);
  WiFi.softAP(ap_ssid, ap_password);
  
  IPAddress IP = WiFi.softAPIP();
  Serial.print("AP IP address: ");
  Serial.println(IP);
}

void initializeWebServer() {
  Serial.println("Setting up web server...");
  
  // Serve static files
  server.serveStatic("/", SPIFFS, "/");
  
  // API endpoints
  server.on("/api/telemetry", HTTP_GET, handleGetTelemetry);
  server.on("/api/arm", HTTP_POST, handleArm);
  server.on("/api/disarm", HTTP_POST, handleDisarm);
  server.on("/api/flightmode", HTTP_POST, handleSetFlightMode);
  server.on("/api/pid", HTTP_POST, handleSetPID);
  server.on("/api/calibrate", HTTP_POST, handleCalibrate);
  server.on("/api/config", HTTP_GET, handleGetConfig);
  server.on("/api/config", HTTP_POST, handleSetConfig);
  
  // CORS headers
  server.enableCORS(true);
  
  server.begin();
  Serial.println("Web server started");
}

void readSensors() {
  // Read MPU6050
  mpu.update();
  flightData.gyroX = mpu.getGyroX();
  flightData.gyroY = mpu.getGyroY();
  flightData.gyroZ = mpu.getGyroZ();
  flightData.accelX = mpu.getAccX();
  flightData.accelY = mpu.getAccY();
  flightData.accelZ = mpu.getAccZ();
  flightData.roll = mpu.getAngleX();
  flightData.pitch = mpu.getAngleY();
  
  // Read compass
  compass.read();
  flightData.magX = compass.getX();
  flightData.magY = compass.getY();
  flightData.magZ = compass.getZ();
  flightData.yaw = compass.getAzimuth();
  
  // Read barometer
  int result = barometer.read();
  if (result == MS5611_READ_OK) {
    flightData.pressure = barometer.getPressure();
    flightData.temperature = barometer.getTemperature();

    // Calculate altitude from pressure using standard atmospheric formula
    // Altitude = 44330 * (1 - (pressure/sea_level_pressure)^(1/5.255))
    // Using sea level pressure of 1013.25 mBar
    float seaLevelPressure = 1013.25; // mBar
    flightData.altitude = 44330.0 * (1.0 - pow(flightData.pressure / seaLevelPressure, 1.0/5.255));
  }
}

void readRCInputs() {
  flightData.rcThrottle = pulseIn(RC_THROTTLE_PIN, HIGH, 30000);
  flightData.rcRoll = pulseIn(RC_ROLL_PIN, HIGH, 30000);
  flightData.rcPitch = pulseIn(RC_PITCH_PIN, HIGH, 30000);
  flightData.rcYaw = pulseIn(RC_YAW_PIN, HIGH, 30000);
  flightData.rcAux1 = pulseIn(RC_AUX1_PIN, HIGH, 30000);
  flightData.rcAux2 = pulseIn(RC_AUX2_PIN, HIGH, 30000);
  flightData.rcAux3 = pulseIn(RC_AUX3_PIN, HIGH, 30000);
  flightData.rcAux4 = pulseIn(RC_AUX4_PIN, HIGH, 30000);
  
  // Safety: If no signal, set to safe values
  if (flightData.rcThrottle == 0) flightData.rcThrottle = 1000;
  if (flightData.rcRoll == 0) flightData.rcRoll = 1500;
  if (flightData.rcPitch == 0) flightData.rcPitch = 1500;
  if (flightData.rcYaw == 0) flightData.rcYaw = 1500;
}

void readGPS() {
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      if (gps.location.isValid()) {
        flightData.gpsConnected = true;
        flightData.gpsLatitude = gps.location.lat();
        flightData.gpsLongitude = gps.location.lng();
        flightData.gpsAltitude = gps.altitude.meters();
        flightData.gpsSatellites = gps.satellites.value();
      } else {
        flightData.gpsConnected = false;
      }
    }
  }
}

void flightControlLoop() {
  if (!flightData.armed) {
    setAllMotors(1000);
    return;
  }
  
  // Get RC inputs as setpoints
  float rollSetpoint = map(flightData.rcRoll, 1000, 2000, -30, 30);
  float pitchSetpoint = map(flightData.rcPitch, 1000, 2000, -30, 30);
  float yawSetpoint = map(flightData.rcYaw, 1000, 2000, -180, 180);
  int throttle = map(flightData.rcThrottle, 1000, 2000, 1000, 2000);
  
  // Apply flight mode logic
  if (flightData.flightMode == "MANUAL") {
    // Direct RC passthrough
    flightData.motorValues[0] = throttle;
    flightData.motorValues[1] = throttle;
    flightData.motorValues[2] = throttle;
    flightData.motorValues[3] = throttle;
  } else if (flightData.flightMode == "STABILIZE") {
    // PID control for stabilization
    float rollOutput = rollPID.compute(rollSetpoint, flightData.roll);
    float pitchOutput = pitchPID.compute(pitchSetpoint, flightData.pitch);
    float yawOutput = yawPID.compute(yawSetpoint, flightData.yaw);
    
    // Motor mixing for quadcopter X configuration
    flightData.motorValues[0] = throttle + rollOutput + pitchOutput - yawOutput; // Front Left
    flightData.motorValues[1] = throttle - rollOutput + pitchOutput + yawOutput; // Front Right
    flightData.motorValues[2] = throttle + rollOutput - pitchOutput + yawOutput; // Back Left
    flightData.motorValues[3] = throttle - rollOutput - pitchOutput - yawOutput; // Back Right
  }
  // Add more flight modes here...
  
  // Constrain motor values
  for (int i = 0; i < 4; i++) {
    flightData.motorValues[i] = constrain(flightData.motorValues[i], 1000, 2000);
  }
  
  // Apply motor values
  motorFL.writeMicroseconds(flightData.motorValues[0]);
  motorFR.writeMicroseconds(flightData.motorValues[1]);
  motorBL.writeMicroseconds(flightData.motorValues[2]);
  motorBR.writeMicroseconds(flightData.motorValues[3]);
}

void setAllMotors(int value) {
  motorFL.writeMicroseconds(value);
  motorFR.writeMicroseconds(value);
  motorBL.writeMicroseconds(value);
  motorBR.writeMicroseconds(value);
  
  for (int i = 0; i < 4; i++) {
    flightData.motorValues[i] = value;
  }
}

void checkBattery() {
  int rawValue = analogRead(BATTERY_PIN);
  flightData.batteryVoltage = (rawValue / 4095.0) * 3.3 * 4.0; // Voltage divider
  flightData.batteryPercentage = map(flightData.batteryVoltage * 100, 1100, 1260, 0, 100);
  flightData.batteryPercentage = constrain(flightData.batteryPercentage, 0, 100);
}

void safetyChecks() {
  // Check for RC signal loss
  static unsigned long lastRCSignal = millis();
  if (flightData.rcThrottle > 900 && flightData.rcThrottle < 2100) {
    lastRCSignal = millis();
  }
  
  if (millis() - lastRCSignal > 2000 && flightData.armed) {
    // RC signal lost - emergency disarm
    flightData.armed = false;
    setAllMotors(1000);
    Serial.println("EMERGENCY: RC signal lost - DISARMED");
  }
  
  // Check battery voltage
  if (flightData.batteryVoltage < 11.0 && flightData.armed) {
    // Low battery - force landing
    Serial.println("WARNING: Low battery voltage");
    // Could implement auto-landing here
  }
  
  // Sensor health checks
  flightData.sensorsOK = true; // Implement actual health checks
}

void updateDisplay() {
  tft.fillScreen(ST77XX_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST77XX_WHITE);
  
  tft.println("QuadFly FC v2.0");
  tft.println("===============");
  
  // Flight status
  tft.setTextColor(flightData.armed ? ST77XX_RED : ST77XX_GREEN);
  tft.println(flightData.armed ? "ARMED" : "DISARMED");
  
  tft.setTextColor(ST77XX_CYAN);
  tft.println("Mode: " + flightData.flightMode);
  
  // Battery
  tft.setTextColor(flightData.batteryPercentage > 20 ? ST77XX_GREEN : ST77XX_RED);
  tft.println("Batt: " + String(flightData.batteryVoltage, 1) + "V (" + String(flightData.batteryPercentage) + "%)");
  
  // GPS
  tft.setTextColor(flightData.gpsConnected ? ST77XX_GREEN : ST77XX_RED);
  tft.println("GPS: " + String(flightData.gpsSatellites) + " sats");
  
  // Attitude
  tft.setTextColor(ST77XX_WHITE);
  tft.println("R:" + String(flightData.roll, 1) + " P:" + String(flightData.pitch, 1) + " Y:" + String(flightData.yaw, 1));
}

void blinkStatusLED(int times) {
  for (int i = 0; i < times; i++) {
    digitalWrite(STATUS_LED_PIN, HIGH);
    delay(200);
    digitalWrite(STATUS_LED_PIN, LOW);
    delay(200);
  }
}

// Web API Handlers
void handleGetTelemetry() {
  StaticJsonDocument<1024> doc;
  
  doc["armed"] = flightData.armed;
  doc["flightMode"] = flightData.flightMode;
  
  JsonObject battery = doc.createNestedObject("battery");
  battery["voltage"] = flightData.batteryVoltage;
  battery["percentage"] = flightData.batteryPercentage;
  
  JsonObject gpsObj = doc.createNestedObject("gps");
  gpsObj["connected"] = flightData.gpsConnected;
  gpsObj["satellites"] = flightData.gpsSatellites;
  gpsObj["latitude"] = flightData.gpsLatitude;
  gpsObj["longitude"] = flightData.gpsLongitude;
  
  JsonObject sensors = doc.createNestedObject("sensors");
  JsonObject gyro = sensors.createNestedObject("gyro");
  gyro["x"] = flightData.gyroX;
  gyro["y"] = flightData.gyroY;
  gyro["z"] = flightData.gyroZ;
  
  JsonObject accel = sensors.createNestedObject("accel");
  accel["x"] = flightData.accelX;
  accel["y"] = flightData.accelY;
  accel["z"] = flightData.accelZ;
  
  JsonObject mag = sensors.createNestedObject("mag");
  mag["x"] = flightData.magX;
  mag["y"] = flightData.magY;
  mag["z"] = flightData.magZ;
  
  sensors["pressure"] = flightData.pressure;
  sensors["altitude"] = flightData.altitude;
  
  JsonObject pid = doc.createNestedObject("pid");
  JsonObject rollPIDObj = pid.createNestedObject("roll");
  rollPIDObj["p"] = rollPID.kp;
  rollPIDObj["i"] = rollPID.ki;
  rollPIDObj["d"] = rollPID.kd;
  
  JsonObject pitchPIDObj = pid.createNestedObject("pitch");
  pitchPIDObj["p"] = pitchPID.kp;
  pitchPIDObj["i"] = pitchPID.ki;
  pitchPIDObj["d"] = pitchPID.kd;
  
  JsonObject yawPIDObj = pid.createNestedObject("yaw");
  yawPIDObj["p"] = yawPID.kp;
  yawPIDObj["i"] = yawPID.ki;
  yawPIDObj["d"] = yawPID.kd;
  
  String response;
  serializeJson(doc, response);
  
  server.send(200, "application/json", response);
}

void handleArm() {
  if (flightData.rcThrottle < 1100) { // Throttle must be low
    flightData.armed = true;
    rollPID.reset();
    pitchPID.reset();
    yawPID.reset();
    server.send(200, "application/json", "{\"status\":\"armed\"}");
    Serial.println("ARMED");
  } else {
    server.send(400, "application/json", "{\"error\":\"Throttle must be low to arm\"}");
  }
}

void handleDisarm() {
  flightData.armed = false;
  setAllMotors(1000);
  server.send(200, "application/json", "{\"status\":\"disarmed\"}");
  Serial.println("DISARMED");
}

void handleSetFlightMode() {
  if (server.hasArg("mode")) {
    String mode = server.arg("mode");
    if (mode == "MANUAL" || mode == "STABILIZE" || mode == "ALTITUDE_HOLD" || 
        mode == "GPS_HOLD" || mode == "ACRO") {
      flightData.flightMode = mode;
      server.send(200, "application/json", "{\"status\":\"ok\"}");
      Serial.println("Flight mode changed to: " + mode);
    } else {
      server.send(400, "application/json", "{\"error\":\"Invalid flight mode\"}");
    }
  } else {
    server.send(400, "application/json", "{\"error\":\"Missing mode parameter\"}");
  }
}

void handleSetPID() {
  StaticJsonDocument<512> doc;
  DeserializationError error = deserializeJson(doc, server.arg("plain"));
  
  if (error) {
    server.send(400, "application/json", "{\"error\":\"Invalid JSON\"}");
    return;
  }
  
  if (doc.containsKey("roll")) {
    rollPID.kp = doc["roll"]["p"];
    rollPID.ki = doc["roll"]["i"];
    rollPID.kd = doc["roll"]["d"];
  }
  
  if (doc.containsKey("pitch")) {
    pitchPID.kp = doc["pitch"]["p"];
    pitchPID.ki = doc["pitch"]["i"];
    pitchPID.kd = doc["pitch"]["d"];
  }
  
  if (doc.containsKey("yaw")) {
    yawPID.kp = doc["yaw"]["p"];
    yawPID.ki = doc["yaw"]["i"];
    yawPID.kd = doc["yaw"]["d"];
  }
  
  saveSettings();
  server.send(200, "application/json", "{\"status\":\"ok\"}");
  Serial.println("PID settings updated");
}

void handleCalibrate() {
  String sensor = server.arg("sensor");
  
  if (sensor == "gyro") {
    mpu.calcGyroOffsets(true);
    server.send(200, "application/json", "{\"status\":\"gyro calibrated\"}");
  } else if (sensor == "compass") {
    // Implement compass calibration
    server.send(200, "application/json", "{\"status\":\"compass calibration started\"}");
  } else {
    server.send(400, "application/json", "{\"error\":\"Unknown sensor\"}");
  }
}

void handleGetConfig() {
  StaticJsonDocument<512> doc;
  
  doc["wifi"]["ssid"] = ap_ssid;
  doc["wifi"]["channel"] = 6;
  doc["wifi"]["power"] = 20;
  
  doc["system"]["name"] = "QuadFly FC v2.0";
  doc["system"]["version"] = "2.0.1-beta";
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

void handleSetConfig() {
  // Implement configuration updates
  server.send(200, "application/json", "{\"status\":\"config updated\"}");
}

void loadSettings() {
  rollPID.kp = preferences.getFloat("roll_p", 0.04);
  rollPID.ki = preferences.getFloat("roll_i", 0.02);
  rollPID.kd = preferences.getFloat("roll_d", 0.01);
  
  pitchPID.kp = preferences.getFloat("pitch_p", 0.04);
  pitchPID.ki = preferences.getFloat("pitch_i", 0.02);
  pitchPID.kd = preferences.getFloat("pitch_d", 0.01);
  
  yawPID.kp = preferences.getFloat("yaw_p", 0.08);
  yawPID.ki = preferences.getFloat("yaw_i", 0.02);
  yawPID.kd = preferences.getFloat("yaw_d", 0.00);
}

void saveSettings() {
  preferences.putFloat("roll_p", rollPID.kp);
  preferences.putFloat("roll_i", rollPID.ki);
  preferences.putFloat("roll_d", rollPID.kd);
  
  preferences.putFloat("pitch_p", pitchPID.kp);
  preferences.putFloat("pitch_i", pitchPID.ki);
  preferences.putFloat("pitch_d", pitchPID.kd);
  
  preferences.putFloat("yaw_p", yawPID.kp);
  preferences.putFloat("yaw_i", yawPID.ki);
  preferences.putFloat("yaw_d", yawPID.kd);
}
