# QuadFly Flight Controller v2.0 - Installation Guide

## Required Arduino Libraries

Install these libraries through Arduino IDE Library Manager or manually:

### Core Libraries (Usually pre-installed)
- WiFi (ESP32 built-in)
- WebServer (ESP32 built-in)
- A<PERSON>uino<PERSON>son (by <PERSON><PERSON>)
- SPIFFS (ESP32 built-in)
- Preferences (ESP32 built-in)
- Wire (built-in)
- SPI (built-in)
- ESP32Servo (by <PERSON>)

### Sensor Libraries
```
MPU6050_tockn (by <PERSON>ckn)
QMC5883LCompass (by MPrograms)
MS5611 (by <PERSON>)
TinyGPS++ (by <PERSON><PERSON>)
```

### Display Libraries
```
Adafruit GFX Library (by Adafruit)
Adafruit ST7735 and ST7789 Library (by Adafruit)
```

## Arduino IDE Setup

1. **Install ESP32 Board Package:**
   - File → Preferences
   - Add to Additional Board Manager URLs: 
     `https://dl.espressif.com/dl/package_esp32_index.json`
   - Tools → Board → Boards Manager
   - Search "ESP32" and install "ESP32 by Espressif Systems"

2. **Board Configuration:**
   - Board: "ESP32 Dev Module"
   - Upload Speed: "921600"
   - CPU Frequency: "240MHz (WiFi/BT)"
   - Flash Frequency: "80MHz"
   - Flash Mode: "QIO"
   - Flash Size: "4MB (32Mb)"
   - Partition Scheme: "Default 4MB with spiffs"

## Hardware Connections

### GY-86 Sensor Module (I2C)
```
GY-86 VCC  → ESP32 3.3V
GY-86 GND  → ESP32 GND
GY-86 SDA  → ESP32 GPIO21
GY-86 SCL  → ESP32 GPIO22
```

### RC Receiver (PWM)
```
CH1 (Roll)     → ESP32 GPIO13
CH2 (Pitch)    → ESP32 GPIO12
CH3 (Throttle) → ESP32 GPIO14
CH4 (Yaw)      → ESP32 GPIO27
CH5 (AUX1)     → ESP32 GPIO26
CH6 (AUX2)     → ESP32 GPIO25
CH7 (AUX3)     → ESP32 GPIO36
CH8 (AUX4)     → ESP32 GPIO34
```

### Motors/ESCs (PWM Output)
```
Motor 1 (Front Left)  → ESP32 GPIO33
Motor 2 (Front Right) → ESP32 GPIO32
Motor 3 (Back Left)   → ESP32 GPIO15
Motor 4 (Back Right)  → ESP32 GPIO19
```

### GPS Module (UART)
```
GPS VCC → ESP32 3.3V
GPS GND → ESP32 GND
GPS TX  → ESP32 GPIO16 (RX2)
GPS RX  → ESP32 GPIO17 (TX2)
```

### TFT Display ST7735 (SPI)
```
TFT VCC → ESP32 3.3V
TFT GND → ESP32 GND
TFT CS  → ESP32 GPIO5
TFT RST → ESP32 GPIO4
TFT DC  → ESP32 GPIO2
TFT SDA → ESP32 GPIO23 (MOSI)
TFT SCL → ESP32 GPIO18 (SCK)
```

### Other Components
```
Battery Voltage Divider → ESP32 GPIO35 (ADC)
Status LED             → ESP32 GPIO2
```

### Battery Voltage Divider Circuit
```
Battery+ ─── 10kΩ ─── GPIO35 ─── 3.3kΩ ─── GND
```
This gives ~4:1 voltage division for 3S LiPo (12.6V max → 3.15V max)

## Power Supply Requirements

- **Main Power:** 5V/3A power supply or 3S LiPo battery (11.1-12.6V) with 5V BEC
- **ESP32:** 3.3V (regulated from 5V)
- **Sensors:** 3.3V
- **GPS:** 3.3V  
- **Display:** 3.3V
- **Motors:** Direct battery voltage through ESCs

## Pre-Flight Checklist

1. **Hardware Check:**
   - All connections secure
   - Propellers removed for initial testing
   - Battery voltage > 11.1V
   - No loose wires

2. **Software Check:**
   - Upload firmware successfully
   - Serial monitor shows "QuadFly Flight Controller Ready!"
   - All sensors initialized without errors
   - WiFi AP "QuadFly_FC_001" visible

3. **Calibration:**
   - Gyroscope calibration (drone must be perfectly still)
   - Accelerometer calibration (6-point calibration)
   - Compass calibration (rotate in all axes)
   - ESC calibration (throttle range)

4. **Testing:**
   - RC transmitter binding and signal check
   - Motor direction test (without props)
   - Flight mode switching
   - Arm/disarm functionality

## Safety Warnings

⚠️ **IMPORTANT SAFETY NOTES:**

1. **Always remove propellers during initial setup and testing**
2. **Never arm the flight controller with propellers attached until fully tested**
3. **Ensure proper RC failsafe configuration**
4. **Test all functions on the bench before flying**
5. **Follow local drone/UAV regulations**
6. **Use appropriate safety gear and fly in safe areas**

## Troubleshooting

### Common Issues:

**"Sensor initialization failed"**
- Check I2C connections (SDA/SCL)
- Verify 3.3V power supply
- Check sensor module orientation

**"No RC signal"**
- Verify RC receiver wiring
- Check transmitter/receiver binding
- Ensure correct PWM signal levels

**"WiFi not appearing"**
- Check ESP32 power supply
- Verify WiFi antenna connection
- Try different WiFi channel

**"Motors not responding"**
- Check ESC power connections
- Verify motor signal wires
- Ensure ESC calibration completed

## Web Interface Access

1. Connect to WiFi network: `QuadFly_FC_001`
2. Password: `quadfly123`
3. Open browser to: `http://192.168.4.1`
4. The flight controller interface should load

## Support

For issues and updates:
- Check serial monitor output for debugging
- Verify all library versions are compatible
- Ensure ESP32 board package is up to date