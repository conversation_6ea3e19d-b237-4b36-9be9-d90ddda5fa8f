@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Aerospace Dark Theme */
    --background: 220 27% 8%;
    --foreground: 213 31% 91%;

    --card: 220 27% 10%;
    --card-foreground: 213 31% 91%;

    --popover: 220 27% 10%;
    --popover-foreground: 213 31% 91%;

    --primary: 199 89% 48%;
    --primary-foreground: 220 27% 8%;
    --primary-glow: 199 89% 65%;

    --secondary: 220 27% 15%;
    --secondary-foreground: 213 31% 91%;

    --muted: 220 27% 12%;
    --muted-foreground: 213 19% 62%;

    --accent: 142 76% 36%;
    --accent-foreground: 213 31% 91%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 213 31% 91%;

    --warning: 38 92% 50%;
    --warning-foreground: 220 27% 8%;

    --border: 220 27% 20%;
    --input: 220 27% 15%;
    --ring: 199 89% 48%;

    --radius: 0.75rem;

    /* Technical gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-surface: linear-gradient(135deg, hsl(var(--card)), hsl(220 27% 12%));
    --gradient-accent: linear-gradient(90deg, hsl(var(--accent) / 0.8), hsl(var(--accent) / 0.4));

    /* Technical shadows */
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);
    --shadow-panel: 0 8px 32px hsl(220 27% 4% / 0.8);
    --shadow-elevated: 0 4px 16px hsl(220 27% 4% / 0.6);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}