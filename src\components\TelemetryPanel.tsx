import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Gauge, Navigation, BarChart3, MapPin } from "lucide-react";

interface FlightData {
  armed: boolean;
  flightMode: string;
  battery: {
    voltage: number;
    percentage: number;
  };
  gps: {
    connected: boolean;
    satellites: number;
    latitude: number;
    longitude: number;
  };
  sensors: {
    gyro: { x: number; y: number; z: number };
    accel: { x: number; y: number; z: number };
    mag: { x: number; y: number; z: number };
    pressure: number;
    altitude: number;
  };
  pid: any;
}

interface TelemetryPanelProps {
  flightData: FlightData;
}

export function TelemetryPanel({ flightData }: TelemetryPanelProps) {
  const formatCoordinate = (coord: number, isLatitude: boolean) => {
    const direction = isLatitude ? (coord >= 0 ? 'N' : 'S') : (coord >= 0 ? 'E' : 'W');
    return `${Math.abs(coord).toFixed(6)}° ${direction}`;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* IMU Sensors */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2">
            <Gauge className="h-5 w-5 text-primary" />
            <span>IMU Sensors</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">Gyroscope (°/s)</span>
            </div>
            <div className="space-y-2 font-mono text-sm">
              <div className="flex justify-between">
                <span>Roll:</span>
                <span className="text-primary">{flightData.sensors.gyro.x.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span>Pitch:</span>
                <span className="text-primary">{flightData.sensors.gyro.y.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span>Yaw:</span>
                <span className="text-primary">{flightData.sensors.gyro.z.toFixed(3)}</span>
              </div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">Accelerometer (m/s²)</span>
            </div>
            <div className="space-y-2 font-mono text-sm">
              <div className="flex justify-between">
                <span>X:</span>
                <span className="text-accent">{flightData.sensors.accel.x.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span>Y:</span>
                <span className="text-accent">{flightData.sensors.accel.y.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span>Z:</span>
                <span className="text-accent">{flightData.sensors.accel.z.toFixed(3)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* GPS & Navigation */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2">
            <Navigation className="h-5 w-5 text-primary" />
            <span>GPS & Navigation</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status</span>
            <Badge variant={flightData.gps.connected ? "default" : "destructive"} className="font-mono">
              {flightData.gps.connected ? "CONNECTED" : "DISCONNECTED"}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Satellites</span>
            <span className="font-mono text-primary">{flightData.gps.satellites}</span>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Latitude:</span>
              <span className="font-mono text-accent text-sm">
                {formatCoordinate(flightData.gps.latitude, true)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Longitude:</span>
              <span className="font-mono text-accent text-sm">
                {formatCoordinate(flightData.gps.longitude, false)}
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Altitude</span>
            <span className="font-mono text-primary">{flightData.sensors.altitude.toFixed(1)}m</span>
          </div>
        </CardContent>
      </Card>

      {/* Environmental Sensors */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            <span>Environmental</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Pressure</span>
            <span className="font-mono text-primary">{flightData.sensors.pressure.toFixed(2)} hPa</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Altitude (Baro)</span>
            <span className="font-mono text-primary">{flightData.sensors.altitude.toFixed(1)}m</span>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">Magnetometer</span>
            </div>
            <div className="space-y-2 font-mono text-sm">
              <div className="flex justify-between">
                <span>X:</span>
                <span className="text-accent">{flightData.sensors.mag.x.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span>Y:</span>
                <span className="text-accent">{flightData.sensors.mag.y.toFixed(3)}</span>
              </div>
              <div className="flex justify-between">
                <span>Z:</span>
                <span className="text-accent">{flightData.sensors.mag.z.toFixed(3)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Battery Status */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2">
            <span>⚡</span>
            <span>Battery Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Voltage</span>
              <span className="font-mono text-primary">{flightData.battery.voltage.toFixed(2)}V</span>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Charge</span>
                <span className="font-mono text-primary">{flightData.battery.percentage}%</span>
              </div>
              <Progress 
                value={flightData.battery.percentage} 
                className="h-2 bg-muted"
              />
            </div>
          </div>

          <div className="text-xs text-muted-foreground space-y-1">
            <div>Cell Count: 3S LiPo</div>
            <div>Capacity: 2200mAh</div>
            <div>Type: Li-Polymer</div>
          </div>
        </CardContent>
      </Card>

      {/* Real-time Activity */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
            <span>System Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">ESC Connection</span>
              <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                ✓ Online
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">RC Signal</span>
              <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                ✓ Strong
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Sensor Health</span>
              <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                ✓ Good
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm">Calibration</span>
              <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                ✓ Complete
              </Badge>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            Last Update: {new Date().toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>

      {/* Data Stream Visualization */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-primary rounded animate-data-stream"></div>
            <span>Data Stream</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Loop Rate</span>
              <span className="font-mono text-primary">1000 Hz</span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Telemetry Rate</span>
              <span className="font-mono text-primary">50 Hz</span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">CPU Load</span>
              <span className="font-mono text-accent">23%</span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Memory Usage</span>
              <span className="font-mono text-accent">67%</span>
            </div>

            <div className="mt-4 p-2 bg-black/20 rounded border">
              <div className="text-xs font-mono text-primary animate-pulse">
                [DATA] Gyro: OK | Accel: OK | Mag: OK | GPS: LOCK
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}