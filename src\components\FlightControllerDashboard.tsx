
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { StatusBar } from "./StatusBar";
import { TelemetryPanel } from "./TelemetryPanel";
import { PIDTuningPanel } from "./PIDTuningPanel";
import { FlightModePanel } from "./FlightModePanel";
import { CalibrationPanel } from "./CalibrationPanel";
import { ConfigurationPanel } from "./ConfigurationPanel";
import { flightControllerAPI, FlightData } from "@/services/flightControllerAPI";
import { AlertTriangle, Wifi, WifiOff } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export function FlightControllerDashboard() {
  const [flightData, setFlightData] = useState<FlightData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch telemetry data from ESP32
  const fetchTelemetryData = async () => {
    try {
      const data = await flightControllerAPI.getTelemetry();
      setFlightData(data);
      setIsConnected(true);
      setError(null);
    } catch (err) {
      setIsConnected(false);
      setError('Failed to connect to flight controller');
      console.error('Telemetry fetch error:', err);
    }
  };

  // Handle arming/disarming
  const handleArmToggle = async () => {
    if (!flightData) return;
    
    try {
      if (flightData.armed) {
        await flightControllerAPI.disarm();
        toast({
          title: "Flight Controller Disarmed",
          description: "Motors stopped, system is safe",
        });
      } else {
        await flightControllerAPI.arm();
        toast({
          title: "Flight Controller Armed",
          description: "CAUTION: Motors are now active!",
          variant: "destructive",
        });
      }
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to toggle arm state",
        variant: "destructive",
      });
    }
  };

  // Handle flight mode changes
  const handleFlightModeChange = async (mode: string) => {
    try {
      await flightControllerAPI.setFlightMode(mode);
      toast({
        title: "Flight Mode Changed",
        description: `Switched to ${mode} mode`,
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to change flight mode",
        variant: "destructive",
      });
    }
  };

  // Handle PID updates
  const handlePIDUpdate = async (pidData: any) => {
    try {
      await flightControllerAPI.setPID(pidData);
      toast({
        title: "PID Settings Updated",
        description: "New PID values saved to flight controller",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to update PID settings",
        variant: "destructive",
      });
    }
  };

  // Real-time telemetry updates
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      await fetchTelemetryData();
      setIsLoading(false);
    };

    fetchData();

    // Set up real-time updates (10Hz)
    const interval = setInterval(fetchTelemetryData, 100);

    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="bg-gradient-surface border-border/50 shadow-elevated">
          <CardContent className="p-8">
            <div className="flex items-center space-x-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <div>
                <h3 className="text-lg font-semibold">Connecting to Flight Controller</h3>
                <p className="text-muted-foreground">Establishing connection to ESP32...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isConnected || !flightData) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-6">
          <Alert className="mb-6 border-destructive/50 bg-destructive/10">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Connection Error:</strong> {error || 'Unable to connect to flight controller'}
              <br />
              <span className="text-sm text-muted-foreground">
                Make sure you're connected to the "QuadFly_FC_001" WiFi network and the flight controller is powered on.
              </span>
            </AlertDescription>
          </Alert>
          
          <Card className="bg-gradient-surface border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <WifiOff className="h-5 w-5 text-destructive" />
                <span>Flight Controller Offline</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground space-y-2">
                  <p><strong>Troubleshooting Steps:</strong></p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Ensure ESP32 is powered and running</li>
                    <li>Connect to "QuadFly_FC_001" WiFi network</li>
                    <li>Check that you're accessing http://***********</li>
                    <li>Verify all sensors are connected properly</li>
                  </ol>
                </div>
                <button 
                  onClick={fetchTelemetryData}
                  className="bg-primary text-primary-foreground px-4 py-2 rounded hover:bg-primary/90"
                >
                  Retry Connection
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <StatusBar 
        flightData={flightData} 
        onArmToggle={handleArmToggle}
        isConnected={isConnected}
      />
      
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
            QuadFly Flight Controller v2.0
          </h1>
          <p className="text-muted-foreground">Professional ESP32-based Flight Control System</p>
          <div className="flex items-center justify-center space-x-2 mt-2">
            <Wifi className={`h-4 w-4 ${isConnected ? 'text-accent' : 'text-destructive'}`} />
            <Badge variant={isConnected ? "default" : "destructive"} className="font-mono">
              {isConnected ? "CONNECTED" : "DISCONNECTED"}
            </Badge>
          </div>
        </div>

        <Tabs defaultValue="telemetry" className="w-full">
          <TabsList className="grid w-full grid-cols-5 bg-card/50 backdrop-blur-sm border border-border/50">
            <TabsTrigger value="telemetry" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              Telemetry
            </TabsTrigger>
            <TabsTrigger value="pid" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              PID Tuning
            </TabsTrigger>
            <TabsTrigger value="modes" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              Flight Modes
            </TabsTrigger>
            <TabsTrigger value="calibration" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              Calibration
            </TabsTrigger>
            <TabsTrigger value="config" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              Configuration
            </TabsTrigger>
          </TabsList>

          <TabsContent value="telemetry" className="mt-6">
            <TelemetryPanel flightData={flightData} />
          </TabsContent>

          <TabsContent value="pid" className="mt-6">
            <PIDTuningPanel 
              flightData={flightData} 
              onPIDUpdate={handlePIDUpdate}
            />
          </TabsContent>

          <TabsContent value="modes" className="mt-6">
            <FlightModePanel 
              flightData={flightData} 
              onFlightModeChange={handleFlightModeChange}
            />
          </TabsContent>

          <TabsContent value="calibration" className="mt-6">
            <CalibrationPanel />
          </TabsContent>

          <TabsContent value="config" className="mt-6">
            <ConfigurationPanel />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
