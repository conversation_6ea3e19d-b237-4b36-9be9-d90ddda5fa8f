
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Battery, Satellite, Wifi, Power, AlertTriangle } from "lucide-react";
import { FlightData } from "@/services/flightControllerAPI";

interface StatusBarProps {
  flightData: FlightData;
  onArmToggle: () => void;
  isConnected: boolean;
}

export function StatusBar({ flightData, onArmToggle, isConnected }: StatusBarProps) {
  const getBatteryColor = (percentage: number) => {
    if (percentage > 50) return "text-accent";
    if (percentage > 20) return "text-warning";
    return "text-destructive";
  };

  return (
    <Card className="bg-gradient-surface border-border/50 shadow-panel">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-6">
          {/* Armed Status */}
          <div className="flex items-center space-x-2">
            <Power className={`h-5 w-5 ${flightData.armed ? 'text-accent animate-pulse-glow' : 'text-muted-foreground'}`} />
            <Badge variant={flightData.armed ? "destructive" : "secondary"} className="font-mono">
              {flightData.armed ? "ARMED" : "DISARMED"}
            </Badge>
          </div>

          {/* Flight Mode */}
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="bg-primary/20 text-primary border-primary/50 font-mono">
              {flightData.flightMode}
            </Badge>
          </div>

          {/* Battery Status */}
          <div className="flex items-center space-x-2">
            <Battery className={`h-5 w-5 ${getBatteryColor(flightData.battery.percentage)}`} />
            <span className={`font-mono ${getBatteryColor(flightData.battery.percentage)}`}>
              {flightData.battery.voltage.toFixed(1)}V ({flightData.battery.percentage}%)
            </span>
          </div>

          {/* GPS Status */}
          <div className="flex items-center space-x-2">
            <Satellite className={`h-5 w-5 ${flightData.gps.connected ? 'text-accent' : 'text-muted-foreground'}`} />
            <span className="font-mono text-foreground">
              {flightData.gps.connected ? `${flightData.gps.satellites} sats` : "No GPS"}
            </span>
          </div>

          {/* WiFi Status */}
          <div className="flex items-center space-x-2">
            <Wifi className={`h-5 w-5 ${isConnected ? 'text-accent animate-pulse' : 'text-destructive'}`} />
            <span className={`font-mono ${isConnected ? 'text-accent' : 'text-destructive'}`}>
              {isConnected ? "Connected" : "Disconnected"}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Safety Warning */}
          {flightData.battery.percentage < 20 && (
            <div className="flex items-center space-x-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-mono">LOW BATTERY</span>
            </div>
          )}

          {/* Connection Warning */}
          {!isConnected && (
            <div className="flex items-center space-x-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-mono">NO CONNECTION</span>
            </div>
          )}

          {/* Arm/Disarm Button */}
          <Button
            variant={flightData.armed ? "destructive" : "outline"}
            onClick={onArmToggle}
            disabled={!isConnected}
            className={`font-mono font-bold ${
              flightData.armed 
                ? 'animate-pulse-glow shadow-glow' 
                : 'border-accent/50 hover:border-accent hover:bg-accent/10'
            }`}
          >
            {flightData.armed ? "DISARM" : "ARM"}
          </Button>
        </div>
      </div>
    </Card>
  );
}
