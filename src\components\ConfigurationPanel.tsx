import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Settings, Wifi, Cpu, HardDrive, RotateCcw, Save, Download, Upload } from "lucide-react";

export function ConfigurationPanel() {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [wifiSettings, setWifiSettings] = useState({
    ssid: "QuadFly_FC_001",
    password: "quadfly123",
    channel: 6,
    power: 20
  });

  const markUnsaved = () => {
    setHasUnsavedChanges(true);
  };

  const saveConfiguration = () => {
    // Simulate saving configuration
    setTimeout(() => {
      setHasUnsavedChanges(false);
    }, 1000);
  };

  const resetToDefaults = () => {
    setWifiSettings({
      ssid: "QuadFly_FC_001",
      password: "quadfly123",
      channel: 6,
      power: 20
    });
    markUnsaved();
  };

  return (
    <div className="space-y-6">
      {/* Configuration Header */}
      <Card className="bg-gradient-surface border-border/50 shadow-elevated">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-primary" />
                <span className="font-semibold">Flight Controller Configuration</span>
              </div>
              {hasUnsavedChanges && (
                <Badge variant="outline" className="bg-warning/20 text-warning border-warning/50 animate-pulse">
                  Unsaved Changes
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={resetToDefaults}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button 
                onClick={saveConfiguration}
                disabled={!hasUnsavedChanges}
                className="bg-primary hover:bg-primary/90 shadow-glow"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Config
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="system" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-card/50 backdrop-blur-sm border border-border/50">
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
          <TabsTrigger value="sensors">Sensors</TabsTrigger>
          <TabsTrigger value="motors">Motors</TabsTrigger>
        </TabsList>

        <TabsContent value="system" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Cpu className="h-5 w-5 text-primary" />
                  <span>System Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Flight Controller Name</Label>
                  <Input 
                    defaultValue="QuadFly FC v2.0" 
                    onChange={markUnsaved}
                    className="bg-background/50"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Firmware Version</Label>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-mono">v2.0.1-beta</span>
                    <Button variant="outline" size="sm">
                      Check Update
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-arm">Auto-arm on Startup</Label>
                    <Switch id="auto-arm" onCheckedChange={markUnsaved} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="led-status">Status LED</Label>
                    <Switch id="led-status" defaultChecked onCheckedChange={markUnsaved} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="buzzer">Buzzer Alerts</Label>
                    <Switch id="buzzer" defaultChecked onCheckedChange={markUnsaved} />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <HardDrive className="h-5 w-5 text-primary" />
                  <span>Data Logging</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="blackbox">Enable Blackbox Logging</Label>
                    <Switch id="blackbox" defaultChecked onCheckedChange={markUnsaved} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="telemetry-log">Log Telemetry Data</Label>
                    <Switch id="telemetry-log" onCheckedChange={markUnsaved} />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Log Level</Label>
                  <Select onValueChange={markUnsaved}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select log level" defaultValue="info" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="error">Error Only</SelectItem>
                      <SelectItem value="warn">Warning</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="debug">Debug</SelectItem>
                      <SelectItem value="verbose">Verbose</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Storage Usage</Label>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used: 2.3GB</span>
                      <span>Free: 5.7GB</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-primary h-2 rounded-full w-1/3"></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="network" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Wifi className="h-5 w-5 text-primary" />
                  <span>WiFi Configuration</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Network Name (SSID)</Label>
                  <Input 
                    value={wifiSettings.ssid}
                    onChange={(e) => {
                      setWifiSettings(prev => ({ ...prev, ssid: e.target.value }));
                      markUnsaved();
                    }}
                    className="bg-background/50"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Password</Label>
                  <Input 
                    type="password"
                    value={wifiSettings.password}
                    onChange={(e) => {
                      setWifiSettings(prev => ({ ...prev, password: e.target.value }));
                      markUnsaved();
                    }}
                    className="bg-background/50"
                  />
                </div>

                <div className="space-y-2">
                  <Label>WiFi Channel</Label>
                  <Select 
                    value={wifiSettings.channel.toString()}
                    onValueChange={(value) => {
                      setWifiSettings(prev => ({ ...prev, channel: parseInt(value) }));
                      markUnsaved();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1,6,11].map(ch => (
                        <SelectItem key={ch} value={ch.toString()}>Channel {ch}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Transmission Power</Label>
                    <span className="font-mono text-primary bg-primary/10 px-2 py-1 rounded text-sm">
                      {wifiSettings.power} dBm
                    </span>
                  </div>
                  <Slider
                    value={[wifiSettings.power]}
                    onValueChange={([value]) => {
                      setWifiSettings(prev => ({ ...prev, power: value }));
                      markUnsaved();
                    }}
                    max={20}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Telemetry Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Telemetry Rate (Hz)</Label>
                  <Slider defaultValue={[50]} max={100} min={10} step={10} onValueChange={markUnsaved} />
                </div>

                <div className="space-y-2">
                  <Label>Web Interface Port</Label>
                  <Input defaultValue="80" onChange={markUnsaved} className="bg-background/50" />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="mavlink">Enable MAVLink</Label>
                    <Switch id="mavlink" onCheckedChange={markUnsaved} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="msp">Enable MSP Protocol</Label>
                    <Switch id="msp" defaultChecked onCheckedChange={markUnsaved} />
                  </div>
                </div>

                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Current IP: ***********</div>
                  <div>MAC: A4:CF:12:34:56:78</div>
                  <div>Connected Clients: 1</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sensors" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Sensor Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Gyro Sample Rate (Hz)</Label>
                  <Slider defaultValue={[1000]} max={2000} min={500} step={100} onValueChange={markUnsaved} />
                </div>

                <div className="space-y-2">
                  <Label>Accelerometer Range (g)</Label>
                  <Select onValueChange={markUnsaved}>
                    <SelectTrigger>
                      <SelectValue placeholder="±8g" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2">±2g</SelectItem>
                      <SelectItem value="4">±4g</SelectItem>
                      <SelectItem value="8">±8g</SelectItem>
                      <SelectItem value="16">±16g</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Magnetometer Declination (°)</Label>
                  <Input defaultValue="12.5" onChange={markUnsaved} className="bg-background/50" />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-mag">Auto Mag Calibration</Label>
                    <Switch id="auto-mag" onCheckedChange={markUnsaved} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="baro-filter">Barometer Filtering</Label>
                    <Switch id="baro-filter" defaultChecked onCheckedChange={markUnsaved} />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Sensor Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>MPU6050 (Gyro/Accel)</span>
                    <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                      ✓ Online
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>HMC5883L (Compass)</span>
                    <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                      ✓ Online
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>MS5611 (Barometer)</span>
                    <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                      ✓ Online
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>GPS Module</span>
                    <Badge variant="default" className="bg-accent/20 text-accent border-accent/50">
                      ✓ Lock
                    </Badge>
                  </div>
                </div>

                <div className="mt-6 space-y-2">
                  <div className="text-sm text-muted-foreground">Last Sensor Update</div>
                  <div className="font-mono text-primary">{new Date().toLocaleTimeString()}</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="motors" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Motor Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Motor Protocol</Label>
                  <Select onValueChange={markUnsaved}>
                    <SelectTrigger>
                      <SelectValue placeholder="DShot600" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dshot150">DShot150</SelectItem>
                      <SelectItem value="dshot300">DShot300</SelectItem>
                      <SelectItem value="dshot600">DShot600</SelectItem>
                      <SelectItem value="dshot1200">DShot1200</SelectItem>
                      <SelectItem value="pwm">PWM</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Motor Idle Throttle (%)</Label>
                  <Slider defaultValue={[8]} max={20} min={0} step={0.5} onValueChange={markUnsaved} />
                </div>

                <div className="space-y-2">
                  <Label>Motor Spin Direction</Label>
                  <Select onValueChange={markUnsaved}>
                    <SelectTrigger>
                      <SelectValue placeholder="Standard" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="reversed">Reversed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="motor-beep">Motor Startup Beep</Label>
                    <Switch id="motor-beep" defaultChecked onCheckedChange={markUnsaved} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="turtle-mode">Turtle Mode</Label>
                    <Switch id="turtle-mode" onCheckedChange={markUnsaved} />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-surface border-border/50">
              <CardHeader>
                <CardTitle>Motor Test</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm text-destructive bg-destructive/10 p-3 rounded border border-destructive/20">
                  ⚠️ Warning: Remove propellers before testing motors!
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <Button variant="outline" className="h-16 flex flex-col">
                    <div className="text-sm">Motor 1</div>
                    <div className="text-xs text-muted-foreground">Front Left</div>
                  </Button>
                  <Button variant="outline" className="h-16 flex flex-col">
                    <div className="text-sm">Motor 2</div>
                    <div className="text-xs text-muted-foreground">Front Right</div>
                  </Button>
                  <Button variant="outline" className="h-16 flex flex-col">
                    <div className="text-sm">Motor 3</div>
                    <div className="text-xs text-muted-foreground">Back Left</div>
                  </Button>
                  <Button variant="outline" className="h-16 flex flex-col">
                    <div className="text-sm">Motor 4</div>
                    <div className="text-xs text-muted-foreground">Back Right</div>
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label>Test Throttle Level</Label>
                  <Slider defaultValue={[20]} max={50} min={10} step={1} />
                </div>

                <Button variant="outline" className="w-full border-destructive/50 hover:bg-destructive/10">
                  Test All Motors
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}